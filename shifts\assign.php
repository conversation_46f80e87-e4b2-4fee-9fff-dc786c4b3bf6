<?php
session_start();

// Include permissions system
require_once __DIR__ . '/../includes/permissions.php';
require_once __DIR__ . '/../includes/attendance_helpers.php';

$mysqli = new mysqli("localhost", "root", "", "attendance_db2");
if ($mysqli->connect_errno) {
  die("Database connection failed.");
}

// Check permissions - require shift assignment permissions
requirePermission($mysqli, 'shifts.assign');

$msg = "";
$error = "";

// Handle POST actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
  if (isset($_POST['action'])) {
    $action = $_POST['action'];

    if ($action === 'assign_single') {
      $employee_id = intval($_POST['employee_id'] ?? 0);
      $shift_template_id = intval($_POST['shift_template_id'] ?? 0);
      $shift_date = $_POST['shift_date'] ?? '';

      if ($employee_id && $shift_template_id && $shift_date) {
        // Check if assignment already exists
        $check_stmt = $mysqli->prepare("SELECT id FROM employee_shifts WHERE employee_id = ? AND shift_date = ?");
        $check_stmt->bind_param('is', $employee_id, $shift_date);
        $check_stmt->execute();
        $check_result = $check_stmt->get_result();
        
        if ($check_result->num_rows > 0) {
          // Update existing assignment
          $stmt = $mysqli->prepare("UPDATE employee_shifts SET shift_template_id = ? WHERE employee_id = ? AND shift_date = ?");
          $stmt->bind_param('iis', $shift_template_id, $employee_id, $shift_date);
          if ($stmt->execute()) {
            $msg = "Shift assignment updated successfully.";
          } else {
            $error = "Failed to update shift assignment.";
          }
        } else {
          // Create new assignment
          $stmt = $mysqli->prepare("INSERT INTO employee_shifts (employee_id, shift_template_id, shift_date) VALUES (?, ?, ?)");
          $stmt->bind_param('iis', $employee_id, $shift_template_id, $shift_date);
          if ($stmt->execute()) {
            $msg = "Shift assigned successfully.";
          } else {
            $error = "Failed to assign shift.";
          }
        }
        $check_stmt->close();
        if (isset($stmt)) $stmt->close();
      } else {
        $error = "Please fill in all required fields.";
      }
    } elseif ($action === 'assign_range') {
      $employee_id = intval($_POST['employee_id'] ?? 0);
      $shift_template_id = intval($_POST['shift_template_id'] ?? 0);
      $start_date = $_POST['start_date'] ?? '';
      $end_date = $_POST['end_date'] ?? '';
      $selected_days = $_POST['days'] ?? [];

      if ($employee_id && $shift_template_id && $start_date && $end_date && !empty($selected_days)) {
        $start = new DateTime($start_date);
        $end = new DateTime($end_date);
        $assignments_created = 0;
        $assignments_updated = 0;

        while ($start <= $end) {
          $day_of_week = $start->format('N'); // 1 = Monday, 7 = Sunday
          
          if (in_array($day_of_week, $selected_days)) {
            $current_date = $start->format('Y-m-d');
            
            // Check if assignment already exists
            $check_stmt = $mysqli->prepare("SELECT id FROM employee_shifts WHERE employee_id = ? AND shift_date = ?");
            $check_stmt->bind_param('is', $employee_id, $current_date);
            $check_stmt->execute();
            $check_result = $check_stmt->get_result();
            
            if ($check_result->num_rows > 0) {
              // Update existing assignment
              $stmt = $mysqli->prepare("UPDATE employee_shifts SET shift_template_id = ? WHERE employee_id = ? AND shift_date = ?");
              $stmt->bind_param('iis', $shift_template_id, $employee_id, $current_date);
              if ($stmt->execute()) {
                $assignments_updated++;
              }
              $stmt->close();
            } else {
              // Create new assignment
              $stmt = $mysqli->prepare("INSERT INTO employee_shifts (employee_id, shift_template_id, shift_date) VALUES (?, ?, ?)");
              $stmt->bind_param('iis', $employee_id, $shift_template_id, $current_date);
              if ($stmt->execute()) {
                $assignments_created++;
              }
              $stmt->close();
            }
            $check_stmt->close();
          }
          
          $start->add(new DateInterval('P1D'));
        }

        if ($assignments_created > 0 || $assignments_updated > 0) {
          $msg = "Successfully processed assignments: $assignments_created created, $assignments_updated updated.";
        } else {
          $error = "No assignments were created. Please check your selection.";
        }
      } else {
        $error = "Please fill in all required fields and select at least one day.";
      }
    }
  }
}

// Fetch employees
$employees_result = $mysqli->query("SELECT id, name FROM employees ORDER BY name");
$employees = $employees_result->fetch_all(MYSQLI_ASSOC);

// Fetch shift templates
$templates_result = $mysqli->query("SELECT id, name, start_time, end_time, description FROM shift_templates ORDER BY name");
$templates = $templates_result->fetch_all(MYSQLI_ASSOC);
?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Assign Shifts - Attendance App</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" />
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.9.1/font/bootstrap-icons.css" rel="stylesheet" />
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet" />
  <link rel="stylesheet" href="../style.css" />
</head>
<body>
  <?php include '../includes/navigation.php'; ?>

  <div class="page-container animate-fade-in">
    <!-- Page Header -->
    <div class="page-header">
      <div class="d-flex justify-content-between align-items-center">
        <div>
          <h1 class="page-title">Assign Shifts</h1>
          <p class="page-subtitle">Assign shift templates to employees for specific dates</p>
        </div>
        <div class="d-flex gap-2">
          <a href="../shifts/templates.php" class="nav-link">
            <i class="bi bi-calendar-week"></i>
            Manage Templates
          </a>
          <a href="../shifts/assignments.php" class="nav-link">
            <i class="bi bi-calendar-check"></i>
            View Assignments
          </a>
          <a href="../dashboard.php" class="nav-link">
            <i class="bi bi-arrow-left"></i>
            Dashboard
          </a>
        </div>
      </div>
    </div>

    <!-- Messages -->
    <?php if ($msg): ?>
      <div class="alert alert-success">
        <i class="bi bi-check-circle me-2"></i>
        <?= htmlspecialchars($msg) ?>
      </div>
    <?php endif; ?>

    <?php if ($error): ?>
      <div class="alert alert-danger">
        <i class="bi bi-exclamation-triangle me-2"></i>
        <?= htmlspecialchars($error) ?>
      </div>
    <?php endif; ?>

    <!-- Assignment Tabs -->
    <div class="content-card">
      <ul class="nav nav-tabs" id="assignmentTabs" role="tablist">
        <li class="nav-item" role="presentation">
          <button class="nav-link active" id="single-tab" data-bs-toggle="tab" data-bs-target="#single" type="button" role="tab">
            <i class="bi bi-calendar-plus me-2"></i>
            Single Date Assignment
          </button>
        </li>
        <li class="nav-item" role="presentation">
          <button class="nav-link" id="range-tab" data-bs-toggle="tab" data-bs-target="#range" type="button" role="tab">
            <i class="bi bi-calendar-range me-2"></i>
            Date Range Assignment
          </button>
        </li>
      </ul>

      <div class="tab-content" id="assignmentTabsContent">
        <!-- Single Date Assignment -->
        <div class="tab-pane fade show active" id="single" role="tabpanel">
          <div class="p-4">
            <h3>Assign Shift for Single Date</h3>
            <p class="text-muted">Assign a shift template to an employee for a specific date.</p>
            
            <form method="POST" class="row g-3">
              <input type="hidden" name="action" value="assign_single" />
              
              <div class="col-md-6">
                <label for="employee_id" class="form-label">Employee</label>
                <select class="form-select" id="employee_id" name="employee_id" required>
                  <option value="">Select Employee</option>
                  <?php foreach ($employees as $employee): ?>
                    <option value="<?= $employee['id'] ?>"><?= htmlspecialchars($employee['name']) ?></option>
                  <?php endforeach; ?>
                </select>
              </div>
              
              <div class="col-md-6">
                <label for="shift_template_id" class="form-label">Shift Template</label>
                <select class="form-select" id="shift_template_id" name="shift_template_id" required>
                  <option value="">Select Shift Template</option>
                  <?php foreach ($templates as $template): ?>
                    <option value="<?= $template['id'] ?>" 
                            data-start="<?= $template['start_time'] ?>" 
                            data-end="<?= $template['end_time'] ?>"
                            data-description="<?= htmlspecialchars($template['description']) ?>">
                      <?= htmlspecialchars($template['name']) ?>
                    </option>
                  <?php endforeach; ?>
                </select>
              </div>
              
              <div class="col-md-6">
                <label for="shift_date" class="form-label">Date</label>
                <input type="date" class="form-control" id="shift_date" name="shift_date" required 
                       min="<?= date('Y-m-d') ?>" />
              </div>
              
              <div class="col-md-6">
                <label class="form-label">Shift Details</label>
                <div id="shift_details" class="form-control-plaintext text-muted">
                  Select a shift template to see details
                </div>
              </div>
              
              <div class="col-12">
                <button type="submit" class="btn btn-primary">
                  <i class="bi bi-check-circle me-1"></i>
                  Assign Shift
                </button>
              </div>
            </form>
          </div>
        </div>

        <!-- Date Range Assignment -->
        <div class="tab-pane fade" id="range" role="tabpanel">
          <div class="p-4">
            <h3>Assign Shift for Date Range</h3>
            <p class="text-muted">Assign a shift template to an employee for multiple dates within a range.</p>
            
            <form method="POST" class="row g-3">
              <input type="hidden" name="action" value="assign_range" />
              
              <div class="col-md-6">
                <label for="range_employee_id" class="form-label">Employee</label>
                <select class="form-select" id="range_employee_id" name="employee_id" required>
                  <option value="">Select Employee</option>
                  <?php foreach ($employees as $employee): ?>
                    <option value="<?= $employee['id'] ?>"><?= htmlspecialchars($employee['name']) ?></option>
                  <?php endforeach; ?>
                </select>
              </div>
              
              <div class="col-md-6">
                <label for="range_shift_template_id" class="form-label">Shift Template</label>
                <select class="form-select" id="range_shift_template_id" name="shift_template_id" required>
                  <option value="">Select Shift Template</option>
                  <?php foreach ($templates as $template): ?>
                    <option value="<?= $template['id'] ?>">
                      <?= htmlspecialchars($template['name']) ?>
                    </option>
                  <?php endforeach; ?>
                </select>
              </div>
              
              <div class="col-md-6">
                <label for="start_date" class="form-label">Start Date</label>
                <input type="date" class="form-control" id="start_date" name="start_date" required 
                       min="<?= date('Y-m-d') ?>" />
              </div>
              
              <div class="col-md-6">
                <label for="end_date" class="form-label">End Date</label>
                <input type="date" class="form-control" id="end_date" name="end_date" required 
                       min="<?= date('Y-m-d') ?>" />
              </div>
              
              <div class="col-12">
                <label class="form-label">Select Days of Week</label>
                <div class="row">
                  <div class="col-md-3">
                    <div class="form-check">
                      <input class="form-check-input" type="checkbox" name="days[]" value="1" id="monday">
                      <label class="form-check-label" for="monday">Monday</label>
                    </div>
                  </div>
                  <div class="col-md-3">
                    <div class="form-check">
                      <input class="form-check-input" type="checkbox" name="days[]" value="2" id="tuesday">
                      <label class="form-check-label" for="tuesday">Tuesday</label>
                    </div>
                  </div>
                  <div class="col-md-3">
                    <div class="form-check">
                      <input class="form-check-input" type="checkbox" name="days[]" value="3" id="wednesday">
                      <label class="form-check-label" for="wednesday">Wednesday</label>
                    </div>
                  </div>
                  <div class="col-md-3">
                    <div class="form-check">
                      <input class="form-check-input" type="checkbox" name="days[]" value="4" id="thursday">
                      <label class="form-check-label" for="thursday">Thursday</label>
                    </div>
                  </div>
                  <div class="col-md-3">
                    <div class="form-check">
                      <input class="form-check-input" type="checkbox" name="days[]" value="5" id="friday">
                      <label class="form-check-label" for="friday">Friday</label>
                    </div>
                  </div>
                  <div class="col-md-3">
                    <div class="form-check">
                      <input class="form-check-input" type="checkbox" name="days[]" value="6" id="saturday">
                      <label class="form-check-label" for="saturday">Saturday</label>
                    </div>
                  </div>
                  <div class="col-md-3">
                    <div class="form-check">
                      <input class="form-check-input" type="checkbox" name="days[]" value="7" id="sunday">
                      <label class="form-check-label" for="sunday">Sunday</label>
                    </div>
                  </div>
                  <div class="col-md-3">
                    <button type="button" class="btn btn-outline-secondary btn-sm" onclick="toggleAllDays()">
                      Toggle All
                    </button>
                  </div>
                </div>
              </div>
              
              <div class="col-12">
                <button type="submit" class="btn btn-primary">
                  <i class="bi bi-calendar-check me-1"></i>
                  Assign Shifts for Range
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>

    <!-- Available Templates -->
    <div class="content-card">
      <h2 class="card-title">
        <i class="bi bi-list-ul me-2"></i>
        Available Shift Templates
      </h2>
      
      <?php if (empty($templates)): ?>
        <div class="text-center py-4">
          <i class="bi bi-calendar-x display-1 text-muted"></i>
          <h4 class="mt-3 text-muted">No Shift Templates Available</h4>
          <p class="text-muted">
            <a href="templates.php">Create shift templates</a> before assigning shifts.
          </p>
        </div>
      <?php else: ?>
        <div class="row">
          <?php foreach ($templates as $template): ?>
            <div class="col-md-6 col-lg-4 mb-3">
              <div class="card h-100">
                <div class="card-body">
                  <h5 class="card-title"><?= htmlspecialchars($template['name']) ?></h5>
                  <p class="card-text">
                    <strong>Time:</strong> 
                    <?= date('g:i A', strtotime($template['start_time'])) ?> - 
                    <?= date('g:i A', strtotime($template['end_time'])) ?>
                  </p>
                  <?php if ($template['description']): ?>
                    <p class="card-text text-muted">
                      <?= htmlspecialchars($template['description']) ?>
                    </p>
                  <?php endif; ?>
                </div>
              </div>
            </div>
          <?php endforeach; ?>
        </div>
      <?php endif; ?>
    </div>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  <script>
    // Update shift details when template is selected
    document.getElementById('shift_template_id').addEventListener('change', function() {
      const selectedOption = this.options[this.selectedIndex];
      const detailsDiv = document.getElementById('shift_details');
      
      if (selectedOption.value) {
        const startTime = selectedOption.dataset.start;
        const endTime = selectedOption.dataset.end;
        const description = selectedOption.dataset.description;
        
        const startFormatted = new Date('2000-01-01 ' + startTime).toLocaleTimeString('en-US', {
          hour: 'numeric',
          minute: '2-digit',
          hour12: true
        });
        const endFormatted = new Date('2000-01-01 ' + endTime).toLocaleTimeString('en-US', {
          hour: 'numeric',
          minute: '2-digit',
          hour12: true
        });
        
        detailsDiv.innerHTML = `
          <strong>Time:</strong> ${startFormatted} - ${endFormatted}<br>
          ${description ? '<strong>Description:</strong> ' + description : ''}
        `;
        detailsDiv.className = 'form-control-plaintext text-info';
      } else {
        detailsDiv.innerHTML = 'Select a shift template to see details';
        detailsDiv.className = 'form-control-plaintext text-muted';
      }
    });

    // Toggle all days function
    function toggleAllDays() {
      const checkboxes = document.querySelectorAll('input[name="days[]"]');
      const allChecked = Array.from(checkboxes).every(cb => cb.checked);
      
      checkboxes.forEach(cb => {
        cb.checked = !allChecked;
      });
    }

    // Set minimum end date based on start date
    document.getElementById('start_date').addEventListener('change', function() {
      document.getElementById('end_date').min = this.value;
    });
  </script>
</body>
</html>
