<?php
session_start();

// Include permissions system
require_once __DIR__ . '/../includes/permissions.php';
require_once __DIR__ . '/../includes/attendance_helpers.php';

$mysqli = new mysqli("localhost", "root", "", "attendance_db2");
if ($mysqli->connect_errno) {
  die("Database connection failed.");
}

// Check permissions - require shift viewing permissions
requirePermission($mysqli, 'shifts.view');

$msg = "";
$error = "";

// Handle POST actions (for removing assignments)
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
  if (isset($_POST['action']) && $_POST['action'] === 'remove_assignment') {
    if (!hasPermission($mysqli, $_SESSION['user_id'], 'shifts.edit')) {
      $error = "You don't have permission to remove shift assignments.";
    } else {
      $assignment_id = intval($_POST['assignment_id'] ?? 0);
      if ($assignment_id) {
        $stmt = $mysqli->prepare("DELETE FROM employee_shifts WHERE id = ?");
        $stmt->bind_param('i', $assignment_id);
        if ($stmt->execute()) {
          $msg = "Shift assignment removed successfully.";
        } else {
          $error = "Failed to remove shift assignment.";
        }
        $stmt->close();
      }
    }
  }
}

// Get filter parameters
$employee_filter = $_GET['employee'] ?? '';
$date_filter = $_GET['date'] ?? '';
$template_filter = $_GET['template'] ?? '';

// Build query with filters
$where_conditions = [];
$params = [];
$param_types = '';

if ($employee_filter) {
  $where_conditions[] = "e.id = ?";
  $params[] = intval($employee_filter);
  $param_types .= 'i';
}

if ($date_filter) {
  $where_conditions[] = "es.shift_date = ?";
  $params[] = $date_filter;
  $param_types .= 's';
}

if ($template_filter) {
  $where_conditions[] = "st.id = ?";
  $params[] = intval($template_filter);
  $param_types .= 'i';
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// Fetch shift assignments with employee and template details
$assignments_query = "
  SELECT 
    es.id,
    es.shift_date,
    e.name as employee_name,
    e.id as employee_id,
    st.name as template_name,
    st.start_time,
    st.end_time,
    st.description
  FROM employee_shifts es
  JOIN employees e ON es.employee_id = e.id
  JOIN shift_templates st ON es.shift_template_id = st.id
  $where_clause
  ORDER BY es.shift_date DESC, e.name ASC
";

if (!empty($params)) {
  $stmt = $mysqli->prepare($assignments_query);
  $stmt->bind_param($param_types, ...$params);
  $stmt->execute();
  $assignments_result = $stmt->get_result();
  $assignments = $assignments_result->fetch_all(MYSQLI_ASSOC);
  $stmt->close();
} else {
  $assignments_result = $mysqli->query($assignments_query);
  $assignments = $assignments_result->fetch_all(MYSQLI_ASSOC);
}

// Fetch employees for filter dropdown
$employees_result = $mysqli->query("SELECT id, name FROM employees ORDER BY name");
$employees = $employees_result->fetch_all(MYSQLI_ASSOC);

// Fetch shift templates for filter dropdown
$templates_result = $mysqli->query("SELECT id, name FROM shift_templates ORDER BY name");
$templates = $templates_result->fetch_all(MYSQLI_ASSOC);

// Check user permissions for UI
$can_edit = hasPermission($mysqli, $_SESSION['user_id'], 'shifts.edit');
$can_assign = hasPermission($mysqli, $_SESSION['user_id'], 'shifts.assign');
?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Shift Assignments - Attendance App</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" />
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.9.1/font/bootstrap-icons.css" rel="stylesheet" />
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet" />
  <link rel="stylesheet" href="../style.css" />
</head>
<body>
  <?php include '../includes/navigation.php'; ?>

  <div class="page-container animate-fade-in">
    <!-- Page Header -->
    <div class="page-header">
      <div class="d-flex justify-content-between align-items-center">
        <div>
          <h1 class="page-title">Shift Assignments</h1>
          <p class="page-subtitle">View and manage current shift assignments</p>
        </div>
        <div class="d-flex gap-2">
          <a href="../shifts/templates.php" class="nav-link">
            <i class="bi bi-calendar-week"></i>
            Manage Templates
          </a>
          <?php if ($can_assign): ?>
          <a href="../shifts/assign.php" class="nav-link">
            <i class="bi bi-person-plus"></i>
            Assign Shifts
          </a>
          <?php endif; ?>
          <a href="../dashboard.php" class="nav-link">
            <i class="bi bi-arrow-left"></i>
            Dashboard
          </a>
        </div>
      </div>
    </div>

    <!-- Messages -->
    <?php if ($msg): ?>
      <div class="alert alert-success">
        <i class="bi bi-check-circle me-2"></i>
        <?= htmlspecialchars($msg) ?>
      </div>
    <?php endif; ?>

    <?php if ($error): ?>
      <div class="alert alert-danger">
        <i class="bi bi-exclamation-triangle me-2"></i>
        <?= htmlspecialchars($error) ?>
      </div>
    <?php endif; ?>

    <!-- Filters -->
    <div class="content-card">
      <h2 class="card-title">
        <i class="bi bi-funnel me-2"></i>
        Filter Assignments
      </h2>
      <form method="GET" class="row g-3">
        <div class="col-md-4">
          <label for="employee" class="form-label">Employee</label>
          <select class="form-select" id="employee" name="employee">
            <option value="">All Employees</option>
            <?php foreach ($employees as $employee): ?>
              <option value="<?= $employee['id'] ?>" <?= $employee_filter == $employee['id'] ? 'selected' : '' ?>>
                <?= htmlspecialchars($employee['name']) ?>
              </option>
            <?php endforeach; ?>
          </select>
        </div>
        
        <div class="col-md-4">
          <label for="date" class="form-label">Date</label>
          <input type="date" class="form-control" id="date" name="date" value="<?= htmlspecialchars($date_filter) ?>" />
        </div>
        
        <div class="col-md-4">
          <label for="template" class="form-label">Shift Template</label>
          <select class="form-select" id="template" name="template">
            <option value="">All Templates</option>
            <?php foreach ($templates as $template): ?>
              <option value="<?= $template['id'] ?>" <?= $template_filter == $template['id'] ? 'selected' : '' ?>>
                <?= htmlspecialchars($template['name']) ?>
              </option>
            <?php endforeach; ?>
          </select>
        </div>
        
        <div class="col-12">
          <button type="submit" class="btn btn-primary">
            <i class="bi bi-search me-1"></i>
            Apply Filters
          </button>
          <a href="assignments.php" class="btn btn-outline-secondary">
            <i class="bi bi-x-circle me-1"></i>
            Clear Filters
          </a>
        </div>
      </form>
    </div>

    <!-- Assignments List -->
    <div class="content-card">
      <h2 class="card-title">
        <i class="bi bi-calendar-check me-2"></i>
        Current Assignments (<?= count($assignments) ?> total)
      </h2>
      
      <?php if (empty($assignments)): ?>
        <div class="text-center py-4">
          <i class="bi bi-calendar-x display-1 text-muted"></i>
          <h4 class="mt-3 text-muted">No Shift Assignments Found</h4>
          <p class="text-muted">
            <?php if ($employee_filter || $date_filter || $template_filter): ?>
              Try adjusting your filters or 
              <a href="assignments.php">clear all filters</a>.
            <?php else: ?>
              Start by <a href="assign.php">assigning shifts to employees</a>.
            <?php endif; ?>
          </p>
        </div>
      <?php else: ?>
        <div class="table-container">
          <table class="table table-hover">
            <thead>
              <tr>
                <th>Employee</th>
                <th>Date</th>
                <th>Shift Template</th>
                <th>Time</th>
                <th>Duration</th>
                <th>Description</th>
                <?php if ($can_edit): ?>
                <th>Actions</th>
                <?php endif; ?>
              </tr>
            </thead>
            <tbody>
            <?php foreach ($assignments as $assignment): ?>
              <tr>
                <td>
                  <strong><?= htmlspecialchars($assignment['employee_name']) ?></strong>
                </td>
                <td>
                  <span class="badge bg-primary">
                    <?= date('M j, Y', strtotime($assignment['shift_date'])) ?>
                  </span>
                </td>
                <td><?= htmlspecialchars($assignment['template_name']) ?></td>
                <td>
                  <?= date('g:i A', strtotime($assignment['start_time'])) ?> - 
                  <?= date('g:i A', strtotime($assignment['end_time'])) ?>
                </td>
                <td>
                  <?php
                  $start = new DateTime($assignment['start_time']);
                  $end = new DateTime($assignment['end_time']);
                  if ($end < $start) $end->add(new DateInterval('P1D')); // Next day
                  $duration = $start->diff($end);
                  echo $duration->format('%h hours %i minutes');
                  ?>
                </td>
                <td><?= htmlspecialchars($assignment['description'] ?: 'No description') ?></td>
                <?php if ($can_edit): ?>
                <td>
                  <form method="POST" style="display: inline;" 
                        onsubmit="return confirm('Are you sure you want to remove this shift assignment?')">
                    <input type="hidden" name="action" value="remove_assignment" />
                    <input type="hidden" name="assignment_id" value="<?= $assignment['id'] ?>" />
                    <button type="submit" class="btn btn-outline-danger btn-sm">
                      <i class="bi bi-trash"></i>
                    </button>
                  </form>
                </td>
                <?php endif; ?>
              </tr>
            <?php endforeach; ?>
            </tbody>
          </table>
        </div>
      <?php endif; ?>
    </div>

    <!-- Quick Stats -->
    <div class="row">
      <div class="col-md-4">
        <div class="content-card text-center">
          <h3 class="text-primary"><?= count($assignments) ?></h3>
          <p class="text-muted mb-0">Total Assignments</p>
        </div>
      </div>
      <div class="col-md-4">
        <div class="content-card text-center">
          <h3 class="text-success"><?= count(array_unique(array_column($assignments, 'employee_id'))) ?></h3>
          <p class="text-muted mb-0">Employees with Shifts</p>
        </div>
      </div>
      <div class="col-md-4">
        <div class="content-card text-center">
          <h3 class="text-info"><?= count(array_unique(array_column($assignments, 'template_name'))) ?></h3>
          <p class="text-muted mb-0">Templates in Use</p>
        </div>
      </div>
    </div>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
