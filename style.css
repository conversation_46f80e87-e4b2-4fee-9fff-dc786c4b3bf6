/* Enhanced Global Styles for Attendance App */
:root {
  /* Light Theme Colors */
  --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  --warning-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  --danger-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
  --info-gradient: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);

  --card-bg: #ffffff;
  --text-primary: #2d3748;
  --text-secondary: #718096;
  --text-muted: #a0aec0;
  --border-color: #e2e8f0;
  --bg-overlay: rgba(255, 255, 255, 0.95);

  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  /* Animation Variables */
  --transition-fast: 0.15s ease-in-out;
  --transition-normal: 0.3s ease-in-out;
  --transition-slow: 0.5s ease-in-out;

  /* Navigation Variables */
  --sidebar-width: 280px;
  --sidebar-collapsed-width: 80px;
  --topbar-height: 70px;
  --nav-bg: var(--card-bg);
  --nav-shadow: var(--shadow-md);
}

/* Dark Theme */
[data-theme="dark"] {
  --card-bg: #1a202c;
  --text-primary: #f7fafc;
  --text-secondary: #cbd5e0;
  --text-muted: #718096;
  --border-color: #4a5568;
  --bg-overlay: rgba(26, 32, 44, 0.95);

  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.3), 0 1px 2px 0 rgba(0, 0, 0, 0.2);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.2);

  --nav-bg: #2d3748;
}

[data-theme="dark"] body {
  background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: var(--text-primary);
  line-height: 1.6;
  min-height: 100vh;
  margin: 0;
  padding: 0;
}

/* Enhanced Typography */
h1, h2, h3, h4, h5, h6 {
  color: var(--text-primary);
  font-weight: 600;
}

h1 {
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Enhanced Buttons */
.btn-primary {
  background: var(--primary-gradient);
  border: none;
  border-radius: 12px;
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: var(--shadow-md);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  background: var(--primary-gradient);
  border: none;
}

.btn-secondary {
  background: var(--card-bg);
  border: 2px solid var(--border-color);
  color: var(--text-primary);
  border-radius: 12px;
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  transition: all 0.3s ease;
}

.btn-secondary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  border-color: #667eea;
  color: #667eea;
}

.btn-success {
  background: var(--success-gradient);
  border: none;
  border-radius: 12px;
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: var(--shadow-md);
}

.btn-success:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  background: var(--success-gradient);
}

.btn-danger {
  background: var(--danger-gradient);
  border: none;
  border-radius: 12px;
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: var(--shadow-md);
}

.btn-danger:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  background: var(--danger-gradient);
}

/* Enhanced Forms */
form label {
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.form-control, .form-select {
  border: 2px solid var(--border-color);
  border-radius: 12px;
  padding: 0.75rem 1rem;
  transition: all 0.3s ease;
  background: var(--card-bg);
}

.form-control:focus, .form-select:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
  background: var(--card-bg);
}

/* Enhanced Cards */
.card {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 16px;
  box-shadow: var(--shadow-md);
  transition: all 0.3s ease;
}

.card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

/* Enhanced Alerts */
.alert {
  border: none;
  border-radius: 12px;
  padding: 1rem 1.5rem;
  font-weight: 500;
}

.alert-success {
  background: linear-gradient(135deg, rgba(67, 233, 123, 0.1) 0%, rgba(56, 249, 215, 0.1) 100%);
  color: #059669;
  border-left: 4px solid #10b981;
}

.alert-danger {
  background: linear-gradient(135deg, rgba(250, 112, 154, 0.1) 0%, rgba(254, 225, 64, 0.1) 100%);
  color: #dc2626;
  border-left: 4px solid #ef4444;
}

.alert-info {
  background: linear-gradient(135deg, rgba(168, 237, 234, 0.1) 0%, rgba(254, 214, 227, 0.1) 100%);
  color: #0891b2;
  border-left: 4px solid #06b6d4;
}

/* Container Enhancements */
.container {
  background: var(--card-bg);
  border-radius: 20px;
  padding: 2rem;
  margin: 2rem auto;
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--border-color);
  max-width: 900px;
}

/* Page Container Styles */
.page-container {
  margin-left: var(--sidebar-width);
  padding: 2rem;
  padding-top: calc(var(--topbar-height) + 2rem);
  min-height: 100vh;
  transition: var(--transition-normal);
}

/* Main Content Area */
.main-content {
  margin-left: var(--sidebar-width);
  padding-top: var(--topbar-height);
  min-height: 100vh;
  transition: var(--transition-normal);
}

.page-header {
  background: var(--card-bg);
  border-radius: 20px;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--border-color);
  position: relative;
  overflow: hidden;
}

.page-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--primary-gradient);
}

.page-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.page-subtitle {
  color: var(--text-secondary);
  font-size: 1.1rem;
  margin-bottom: 0;
}

/* Content Cards */
.content-card {
  background: var(--card-bg);
  border-radius: 16px;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-color);
  transition: all 0.3s ease;
}

.content-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.card-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--text-primary);
}

/* Enhanced Form Styles */
.form-group {
  margin-bottom: 1.5rem;
}

.form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--text-primary);
}

.form-control {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 2px solid var(--border-color);
  border-radius: 12px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: var(--card-bg);
}

.form-control:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-select {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 2px solid var(--border-color);
  border-radius: 12px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: var(--card-bg);
  cursor: pointer;
}

.form-select:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Enhanced Button Styles */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.3s ease;
  gap: 0.5rem;
}

.btn-primary {
  background: var(--primary-gradient);
  color: white;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  color: white;
}

.btn-success {
  background: var(--success-gradient);
  color: white;
}

.btn-success:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  color: white;
}

.btn-danger {
  background: var(--danger-gradient);
  color: white;
}

.btn-danger:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  color: white;
}

.btn-secondary {
  background: var(--text-secondary);
  color: white;
}

.btn-secondary:hover {
  background: var(--text-primary);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  color: white;
}

.btn-sm {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
}

/* Enhanced Table Styles */
.table-container {
  background: var(--card-bg);
  border-radius: 16px;
  overflow: hidden;
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-color);
}

.table {
  margin-bottom: 0;
  border-collapse: separate;
  border-spacing: 0;
}

.table thead th {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: none;
  padding: 1rem;
  font-weight: 600;
  color: var(--text-primary);
  border-bottom: 2px solid var(--border-color);
}

.table tbody td {
  padding: 1rem;
  border: none;
  border-bottom: 1px solid var(--border-color);
  vertical-align: middle;
}

.table tbody tr:hover {
  background-color: rgba(102, 126, 234, 0.05);
}

.table tbody tr:last-child td {
  border-bottom: none;
}

/* Alert Styles */
.alert {
  padding: 1rem 1.5rem;
  border-radius: 12px;
  margin-bottom: 1.5rem;
  border: none;
  font-weight: 500;
}

.alert-success {
  background: linear-gradient(135deg, rgba(67, 233, 123, 0.1) 0%, rgba(56, 249, 215, 0.1) 100%);
  color: #059669;
  border-left: 4px solid #10b981;
}

.alert-danger {
  background: linear-gradient(135deg, rgba(250, 112, 154, 0.1) 0%, rgba(254, 225, 64, 0.1) 100%);
  color: #dc2626;
  border-left: 4px solid #ef4444;
}

.alert-info {
  background: linear-gradient(135deg, rgba(168, 237, 234, 0.1) 0%, rgba(254, 214, 227, 0.1) 100%);
  color: #0369a1;
  border-left: 4px solid #3b82f6;
}

/* Navigation Styles */
.nav-link {
  display: inline-flex;
  align-items: center;
  padding: 0.75rem 1.5rem;
  color: var(--text-secondary);
  text-decoration: none;
  border-radius: 12px;
  transition: all 0.3s ease;
  gap: 0.5rem;
}

.nav-link:hover {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
  transform: translateY(-1px);
}

.nav-link.active {
  background: var(--primary-gradient);
  color: white;
}

/* Sidebar Styles */
.sidebar {
  position: fixed;
  top: 0;
  left: 0;
  width: var(--sidebar-width);
  height: 100vh;
  background: var(--nav-bg);
  border-right: 1px solid var(--border-color);
  box-shadow: var(--nav-shadow);
  z-index: 1000;
  transition: var(--transition-normal);
  overflow-y: auto;
}

.sidebar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-color);
}

.sidebar-brand {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  text-decoration: none;
  color: var(--text-primary);
  font-weight: 700;
  font-size: 1.25rem;
}

.sidebar-brand-icon {
  width: 40px;
  height: 40px;
  background: var(--primary-gradient);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.25rem;
}

.sidebar-toggle {
  display: none;
  background: none;
  border: none;
  font-size: 1.5rem;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 8px;
  transition: var(--transition-fast);
}

.sidebar-toggle:hover {
  background: rgba(102, 126, 234, 0.1);
  color: var(--primary-color);
}

.sidebar-nav {
  padding: 1rem 0;
}

.sidebar-menu {
  list-style: none;
  margin: 0;
  padding: 0;
}

.sidebar-item {
  margin: 0.25rem 0;
}

.sidebar-link {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1.5rem;
  color: var(--text-secondary);
  text-decoration: none;
  transition: var(--transition-fast);
  font-weight: 500;
  border-radius: 0;
}

.sidebar-link:hover {
  background: rgba(102, 126, 234, 0.1);
  color: var(--primary-color);
}

.sidebar-link.active {
  background: var(--primary-gradient);
  color: white;
  position: relative;
}

.sidebar-link.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: white;
}

.sidebar-link i {
  width: 20px;
  text-align: center;
  font-size: 1.1rem;
}

.sidebar-section-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem 1.5rem 0.5rem;
  color: var(--text-muted);
  font-weight: 600;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.sidebar-section-title i {
  width: 20px;
  text-align: center;
}

/* Sidebar Subsections */
.sidebar-subsection {
  margin-left: 0;
}

.sidebar-subsection-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1.5rem;
  color: var(--text-secondary);
  font-weight: 600;
  font-size: 0.9rem;
  cursor: pointer;
  transition: var(--transition-fast);
}

.sidebar-subsection-title:hover {
  background: rgba(102, 126, 234, 0.1);
  color: var(--primary-color);
}

.sidebar-subsection-title i {
  width: 20px;
  text-align: center;
  font-size: 1rem;
}

.sidebar-submenu {
  list-style: none;
  margin: 0;
  padding: 0;
  background: rgba(0, 0, 0, 0.02);
}

.sidebar-submenu li {
  margin: 0;
}

.sidebar-sublink {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem 1.5rem 0.5rem 3rem;
  color: var(--text-secondary);
  text-decoration: none;
  transition: var(--transition-fast);
  font-weight: 400;
  font-size: 0.9rem;
  border-radius: 0;
}

.sidebar-sublink:hover {
  background: rgba(102, 126, 234, 0.1);
  color: var(--primary-color);
}

.sidebar-sublink.active {
  background: var(--primary-gradient);
  color: white;
  position: relative;
}

.sidebar-sublink.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: white;
}

.sidebar-sublink i {
  width: 16px;
  text-align: center;
  font-size: 0.9rem;
}

/* Topbar Styles */
.topbar {
  position: fixed;
  top: 0;
  left: var(--sidebar-width);
  right: 0;
  height: var(--topbar-height);
  background: var(--nav-bg);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--border-color);
  box-shadow: var(--nav-shadow);
  z-index: 999;
  transition: var(--transition-normal);
}

.topbar-container {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 2rem;
}

.mobile-menu-toggle {
  display: none;
  background: none;
  border: none;
  font-size: 1.5rem;
  color: var(--text-primary);
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 8px;
  transition: var(--transition-fast);
}

.mobile-menu-toggle:hover {
  background: rgba(102, 126, 234, 0.1);
  color: var(--primary-color);
}

.topbar-title {
  flex: 1;
}

.page-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
  color: var(--text-primary);
}

/* User Dropdown */
.topbar-user {
  position: relative;
}

.user-dropdown-toggle {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem 1rem;
  background: none;
  border: 1px solid var(--border-color);
  border-radius: 12px;
  cursor: pointer;
  transition: var(--transition-fast);
  color: var(--text-primary);
}

.user-dropdown-toggle:hover {
  border-color: var(--primary-color);
  background: rgba(102, 126, 234, 0.05);
}

.user-avatar {
  font-size: 1.5rem;
  color: var(--text-secondary);
}

.user-name {
  font-weight: 500;
  font-size: 0.9rem;
}

.user-dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  min-width: 280px;
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  box-shadow: var(--shadow-lg);
  z-index: 1001;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all var(--transition-fast);
  margin-top: 0.5rem;
}

.user-dropdown-menu.show {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.user-dropdown-header {
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-color);
}

.user-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.user-avatar-large {
  font-size: 2.5rem;
  color: var(--text-secondary);
}

.user-name-large {
  font-weight: 600;
  font-size: 1.1rem;
  color: var(--text-primary);
}

.user-role {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.user-dropdown-body {
  padding: 0.5rem 0;
}

.user-dropdown-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1.5rem;
  color: var(--text-secondary);
  text-decoration: none;
  transition: var(--transition-fast);
  font-weight: 500;
  border: none;
  background: none;
  width: 100%;
  cursor: pointer;
}

.user-dropdown-item:hover {
  background: rgba(102, 126, 234, 0.1);
  color: var(--primary-color);
}

.user-dropdown-item.logout-item:hover {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.user-dropdown-item i {
  width: 20px;
  text-align: center;
}

/* Toggle Switch */
.toggle-switch {
  width: 40px;
  height: 20px;
  background: var(--border-color);
  border-radius: 10px;
  position: relative;
  transition: var(--transition-fast);
  margin-left: auto;
}

.toggle-switch.active {
  background: var(--primary-gradient);
}

.toggle-slider {
  width: 16px;
  height: 16px;
  background: white;
  border-radius: 50%;
  position: absolute;
  top: 2px;
  left: 2px;
  transition: var(--transition-fast);
  box-shadow: var(--shadow-sm);
}

.toggle-switch.active .toggle-slider {
  transform: translateX(20px);
}

/* Sidebar Overlay for Mobile */
.sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 999;
  opacity: 0;
  visibility: hidden;
  transition: var(--transition-normal);
}

.sidebar-overlay.active {
  opacity: 1;
  visibility: visible;
}

/* Dropdown Styles */
.dropdown {
  position: relative;
}

.dropdown-toggle {
  position: relative;
}

.dropdown-arrow {
  font-size: 0.75rem;
  margin-left: 0.25rem;
  transition: transform var(--transition-fast);
}

.dropdown-toggle[aria-expanded="true"] .dropdown-arrow {
  transform: rotate(180deg);
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  min-width: 200px;
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  box-shadow: var(--shadow-lg);
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all var(--transition-fast);
  list-style: none;
  margin: 0;
  padding: 0.5rem 0;
}

.dropdown-menu.show {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.dropdown-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  color: var(--text-secondary);
  text-decoration: none;
  transition: var(--transition-fast);
  font-weight: 500;
}

.dropdown-item:hover {
  background: rgba(102, 126, 234, 0.1);
  color: var(--primary-color);
}

.dropdown-item.active {
  background: var(--primary-gradient);
  color: white;
}

.dropdown-item i {
  width: 16px;
  text-align: center;
}

/* Mobile Navigation */
.nav-toggle {
  display: none;
  background: none;
  border: none;
  font-size: 1.5rem;
  color: var(--text-primary);
  cursor: pointer;
}

/* Loading States */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--bg-overlay);
  backdrop-filter: blur(5px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  opacity: 0;
  visibility: hidden;
  transition: var(--transition-normal);
}

.loading-overlay.active {
  opacity: 1;
  visibility: visible;
}

.loading-spinner {
  width: 60px;
  height: 60px;
  border: 4px solid var(--border-color);
  border-top: 4px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.btn-loading {
  position: relative;
  pointer-events: none;
  opacity: 0.7;
}

.btn-loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.btn-loading .btn-text {
  opacity: 0;
}

/* Animations */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes slideIn {
  from { transform: translateX(-100%); }
  to { transform: translateX(0); }
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% { transform: translateY(0); }
  40%, 43% { transform: translateY(-10px); }
  70% { transform: translateY(-5px); }
  90% { transform: translateY(-2px); }
}

/* Animation Classes */
.animate-fade-in {
  animation: fadeIn 0.5s ease-out;
}

.animate-slide-in {
  animation: slideIn 0.3s ease-out;
}

.animate-pulse {
  animation: pulse 2s infinite;
}

.animate-bounce {
  animation: bounce 1s;
}

/* Page Transitions */
.page-transition {
  opacity: 0;
  transform: translateY(20px);
  transition: var(--transition-normal);
}

.page-transition.loaded {
  opacity: 1;
  transform: translateY(0);
}

/* Enhanced Hover Effects */
.hover-lift {
  transition: var(--transition-fast);
}

.hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.hover-glow {
  transition: var(--transition-fast);
}

.hover-glow:hover {
  box-shadow: 0 0 20px rgba(102, 126, 234, 0.3);
}

/* Notification Styles */
.notification {
  position: fixed;
  top: calc(var(--nav-height) + 1rem);
  right: 1rem;
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 1rem 1.5rem;
  box-shadow: var(--shadow-lg);
  z-index: 1001;
  transform: translateX(100%);
  transition: var(--transition-normal);
  max-width: 400px;
}

.notification.show {
  transform: translateX(0);
}

.notification.success {
  border-left: 4px solid var(--success-color);
}

.notification.error {
  border-left: 4px solid var(--danger-color);
}

.notification.info {
  border-left: 4px solid var(--info-color);
}

/* Responsive Enhancements */
/* Mobile Layout */
@media (max-width: 768px) {
  /* Hide sidebar by default on mobile */
  .sidebar {
    transform: translateX(-100%);
  }

  .sidebar.active {
    transform: translateX(0);
  }

  .sidebar-toggle {
    display: block;
  }

  /* Adjust topbar for mobile */
  .topbar {
    left: 0;
  }

  .mobile-menu-toggle {
    display: block;
  }

  /* Adjust main content for mobile */
  .page-container,
  .main-content {
    margin-left: 0;
    padding-top: calc(var(--topbar-height) + 1rem);
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .page-header {
    padding: 1.5rem;
    border-radius: 16px;
  }

  .page-title {
    font-size: 1.25rem;
  }

  .content-card {
    padding: 1.5rem;
    border-radius: 12px;
  }

  .btn {
    width: 100%;
    margin-bottom: 0.5rem;
  }

  .table-container {
    overflow-x: auto;
  }

  /* User dropdown adjustments for mobile */
  .user-dropdown-menu {
    right: 1rem;
    left: 1rem;
    min-width: auto;
  }

  .user-name {
    display: none;
  }

  .topbar-container {
    padding: 0 1rem;
  }
}

/* Very small screens */
@media (max-width: 480px) {
  .page-container,
  .main-content {
    padding: 0.75rem;
    padding-top: calc(var(--topbar-height) + 0.75rem);
  }

  .page-header {
    padding: 1.25rem;
    margin-bottom: 1.5rem;
  }

  .page-title {
    font-size: 1.1rem;
  }

  .content-card {
    padding: 1rem;
    border-radius: 12px;
  }

  .sidebar-brand-text {
    display: none;
  }

  .sidebar {
    width: 280px;
  }
}
